# from langchain_community.utilities import DuckDuckGoSearchAPIWrapper

# def search(query: str, results = 5, region = "wt-wt", time="y") -> str:
#     # Create an instance with custom parameters
#     api = DuckDuckGoSearchAPIWrapper(
#         region=region,  # Set the region for search results
#         safesearch="off",  # Set safesearch level (options: strict, moderate, off)
#         time=time,  # Set time range (options: d, w, m, y)
#         max_results=results  # Set maximum number of results to return
#     )
#     # Perform a search
#     result = api.run(query)
#     return result

from ddgs import DDGS
from typing import List, Dict, Any

def search(query: str, results = 5, region = "wt-wt", time="y") -> List[Dict[str, Any]]:
    """
    البحث باستخدام DuckDuckGo مع إرجاع النتائج بتنسيق منظم
    """
    ddgs = DDGS()
    src = ddgs.text(
        query,
        region=region,  # Specify region
        safesearch="off",  # SafeSearch setting
        timelimit=time,  # Time limit (y = past year)
        max_results=results  # Number of results to return
    )

    formatted_results = []
    for s in src:
        if isinstance(s, dict):
            # إذا كانت النتيجة dict بالفعل، استخدمها مباشرة
            formatted_results.append(s)
        else:
            # إذا كانت string، حولها لـ dict
            formatted_results.append({"raw": str(s)})

    return formatted_results