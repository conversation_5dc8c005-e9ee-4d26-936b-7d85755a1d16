print("Testing local search...")

try:
    from python.helpers.duckduckgo_search import search as ddg_search
    print("✅ DuckDuckGo import successful")
    
    # Test DuckDuckGo search
    results = ddg_search("test", results=2)
    print(f"✅ DuckDuckGo search returned {len(results)} results")
    print(f"First result type: {type(results[0]) if results else 'No results'}")
    
except Exception as e:
    print(f"❌ DuckDuckGo test failed: {e}")

print("Test completed.")
