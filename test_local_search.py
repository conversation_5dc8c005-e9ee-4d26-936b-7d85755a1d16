#!/usr/bin/env python3
"""
اختبار خدمة البحث المحلية الجديدة
"""
import asyncio
import sys
import os

# إضافة مسار المشروع للـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python.helpers.local_search import search
from python.helpers.print_style import PrintStyle

async def test_search():
    """
    اختبار دالة البحث
    """
    print("🔍 اختبار خدمة البحث المحلية...")
    
    # اختبار بحث بسيط
    query = "Python programming"
    print(f"البحث عن: {query}")
    
    try:
        results = await search(query, num_results=3)
        
        print(f"✅ تم العثور على {len(results.get('results', []))} نتيجة")
        
        for i, result in enumerate(results.get('results', [])[:3]):
            print(f"\n--- النتيجة {i+1} ---")
            print(f"العنوان: {result.get('title', 'غير متوفر')}")
            print(f"الرابط: {result.get('url', 'غير متوفر')}")
            print(f"المحتوى: {result.get('content', 'غير متوفر')[:100]}...")
            print(f"المحرك: {result.get('engine', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
        return False

async def test_searxng_compatibility():
    """
    اختبار التوافق مع searxng.py
    """
    print("\n🔄 اختبار التوافق مع searxng.py...")
    
    try:
        from python.helpers.searxng import search as searxng_search
        
        query = "artificial intelligence"
        print(f"البحث عن: {query}")
        
        results = await searxng_search(query)
        
        print(f"✅ searxng.py يعمل بشكل صحيح")
        print(f"عدد النتائج: {len(results.get('results', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في searxng.py: {str(e)}")
        return False

async def main():
    """
    تشغيل جميع الاختبارات
    """
    print("🚀 بدء اختبار خدمة البحث المحلية\n")
    
    # اختبار الخدمة المباشرة
    test1_passed = await test_search()
    
    # اختبار التوافق مع searxng
    test2_passed = await test_searxng_compatibility()
    
    print("\n" + "="*50)
    if test1_passed and test2_passed:
        print("🎉 جميع الاختبارات نجحت! الخدمة جاهزة للاستخدام")
    else:
        print("⚠️  بعض الاختبارات فشلت، يرجى مراجعة الأخطاء أعلاه")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
