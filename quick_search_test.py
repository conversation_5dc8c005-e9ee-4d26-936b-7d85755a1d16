#!/usr/bin/env python3
"""
اختبار سريع لخدمة البحث
"""
import asyncio
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_search():
    print("🔍 اختبار البحث السريع...")
    
    try:
        # اختبار البحث المحلي مباشرة
        from python.helpers.local_search import search
        
        print("جاري البحث...")
        results = await search("test query", num_results=2)
        
        print(f"✅ البحث نجح! عدد النتائج: {len(results.get('results', []))}")
        
        if results.get('results'):
            first_result = results['results'][0]
            print(f"أول نتيجة: {first_result.get('title', 'بدون عنوان')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_search())
