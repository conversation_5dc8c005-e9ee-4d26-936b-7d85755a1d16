#!/usr/bin/env python3
"""
اختبار Gemini مباشرة
"""
import google.generativeai as genai
from python.helpers import dotenv
import os

def test_gemini():
    """
    اختبار Gemini مباشرة
    """
    print("🔍 اختبار Gemini مباشرة...")
    
    try:
        # الحصول على API key
        api_key = dotenv.get_dotenv_value("API_KEY_GOOGLE")
        if not api_key:
            # جرب طريقة مختلفة
            api_key = os.getenv("API_KEY_GOOGLE")

        if not api_key:
            # اقرأ من الملف مباشرة
            try:
                with open('.env', 'r') as f:
                    for line in f:
                        if line.startswith('API_KEY_GOOGLE='):
                            api_key = line.split('=', 1)[1].strip()
                            break
            except:
                pass

        print(f"API Key: {api_key[:10]}..." if api_key else "لا يوجد API key")
        
        if not api_key:
            print("❌ لا يوجد Google API key")
            return False
        
        # إعداد Gemini
        genai.configure(api_key=api_key)
        
        # اختبار النموذج
        model = genai.GenerativeModel('gemini-2.5-flash-lite-preview-06-17')
        
        response = model.generate_content("Hello, how are you?")
        
        print(f"✅ Gemini يعمل بشكل صحيح!")
        print(f"الاستجابة: {response.text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في Gemini: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_gemini()
