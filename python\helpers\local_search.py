import aiohttp
import asyncio
from python.helpers import dotenv, duckduckgo_search
from python.helpers.print_style import PrintStyle
import json
from typing import Dict, List, Any


class LocalSearchEngine:
    """
    خدمة بحث محلية تستبدل SearXNG
    تستخدم Google Custom Search API أو DuckDuckGo كبديل
    """
    
    def __init__(self):
        self.google_api_key = dotenv.get_dotenv_value("API_KEY_GOOGLE")
        self.google_search_engine_id = dotenv.get_dotenv_value("GOOGLE_SEARCH_ENGINE_ID") or "017576662512468239146:omuauf_lfve"
        
    async def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        البحث باستخدام Google Custom Search API أو DuckDuckGo كبديل
        """
        try:
            # جرب Google Custom Search API أولاً
            if self.google_api_key:
                PrintStyle.debug(f"البحث باستخدام Google Custom Search API: {query}")
                return await self._google_search(query, num_results)
            else:
                PrintStyle.debug(f"البحث باستخدام DuckDuckGo: {query}")
                return await self._duckduckgo_search(query, num_results)
                
        except Exception as e:
            PrintStyle.error(f"خطأ في البحث: {str(e)}")
            # في حالة فشل Google، استخدم DuckDuckGo كبديل
            try:
                PrintStyle.debug("التبديل إلى DuckDuckGo كبديل")
                return await self._duckduckgo_search(query, num_results)
            except Exception as fallback_error:
                PrintStyle.error(f"خطأ في البديل DuckDuckGo: {str(fallback_error)}")
                return {"results": [], "error": str(fallback_error)}

    async def _google_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """
        البحث باستخدام Google Custom Search API
        """
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.google_api_key,
            "cx": self.google_search_engine_id,
            "q": query,
            "num": min(num_results, 10),  # Google API يدعم حد أقصى 10 نتائج لكل طلب
            "safe": "off"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_google_results(data)
                else:
                    error_text = await response.text()
                    raise Exception(f"Google API error {response.status}: {error_text}")

    async def _duckduckgo_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """
        البحث باستخدام DuckDuckGo
        """
        # استخدم DuckDuckGo search helper الموجود
        results = await asyncio.to_thread(
            duckduckgo_search.search, 
            query, 
            results=num_results
        )
        
        return self._format_duckduckgo_results(results)

    def _format_google_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تنسيق نتائج Google لتتوافق مع تنسيق SearXNG
        """
        results = []
        
        if "items" in data:
            for item in data["items"]:
                result = {
                    "title": item.get("title", ""),
                    "url": item.get("link", ""),
                    "content": item.get("snippet", ""),
                    "engine": "google"
                }
                results.append(result)
        
        return {
            "results": results,
            "query": data.get("queries", {}).get("request", [{}])[0].get("searchTerms", ""),
            "number_of_results": len(results)
        }

    def _format_duckduckgo_results(self, raw_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تنسيق نتائج DuckDuckGo لتتوافق مع تنسيق SearXNG
        """
        results = []

        for raw_result in raw_results:
            try:
                if isinstance(raw_result, dict):
                    result = {
                        "title": raw_result.get("title", ""),
                        "url": raw_result.get("href", ""),
                        "content": raw_result.get("body", ""),
                        "engine": "duckduckgo"
                    }
                    results.append(result)
                else:
                    PrintStyle.error(f"نتيجة DuckDuckGo غير متوقعة: {type(raw_result)}")
            except Exception as e:
                PrintStyle.error(f"خطأ في تنسيق نتيجة DuckDuckGo: {str(e)}")
                continue

        return {
            "results": results,
            "query": "",
            "number_of_results": len(results)
        }


# إنشاء instance واحد للاستخدام
_local_search_instance = None

async def search(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    دالة البحث الرئيسية - بديل مباشر لـ searxng.search
    """
    global _local_search_instance
    
    if _local_search_instance is None:
        _local_search_instance = LocalSearchEngine()
    
    return await _local_search_instance.search(query, num_results)


# للتوافق مع الكود الحالي
async def local_search(query: str) -> Dict[str, Any]:
    """
    دالة مساعدة للتوافق مع الكود الحالي
    """
    return await search(query)
