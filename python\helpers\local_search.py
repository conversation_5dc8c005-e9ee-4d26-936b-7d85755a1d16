import aiohttp
import asyncio
from python.helpers import dotenv, duckduckgo_search
from python.helpers.print_style import PrintStyle
import json
from typing import Dict, List, Any
import google.generativeai as genai


class LocalSearchEngine:
    """
    خدمة بحث محلية تستبدل SearXNG
    تستخدم Google Custom Search API أو DuckDuckGo كبديل
    """
    
    def __init__(self):
        self.google_api_key = dotenv.get_dotenv_value("API_KEY_GOOGLE")
        if not self.google_api_key:
            # جرب قراءة من الملف مباشرة
            try:
                with open('.env', 'r') as f:
                    for line in f:
                        if line.startswith('API_KEY_GOOGLE='):
                            self.google_api_key = line.split('=', 1)[1].strip()
                            break
            except:
                pass

        self.google_search_engine_id = dotenv.get_dotenv_value("GOOGLE_SEARCH_ENGINE_ID") or "017576662512468239146:omuauf_lfve"

        # إعداد Gemini للبحث
        if self.google_api_key:
            genai.configure(api_key=self.google_api_key)
        
    async def search(self, query: str, num_results: int = 10) -> Dict[str, Any]:
        """
        البحث باستخدام Google Custom Search API أو DuckDuckGo كبديل
        """
        try:
            # جرب Gemini للبحث أولاً
            if self.google_api_key:
                PrintStyle.debug(f"البحث باستخدام Gemini: {query}")
                return await self._gemini_search(query, num_results)
            else:
                PrintStyle.debug(f"البحث باستخدام DuckDuckGo: {query}")
                return await self._duckduckgo_search(query, num_results)
                
        except Exception as e:
            PrintStyle.error(f"خطأ في البحث: {str(e)}")
            # في حالة فشل Google، استخدم DuckDuckGo كبديل
            try:
                PrintStyle.debug("التبديل إلى DuckDuckGo كبديل")
                return await self._duckduckgo_search(query, num_results)
            except Exception as fallback_error:
                PrintStyle.error(f"خطأ في البديل DuckDuckGo: {str(fallback_error)}")
                return {"results": [], "error": str(fallback_error)}

    async def _gemini_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """
        البحث باستخدام Gemini 2.5 Flash Lite
        """
        try:
            # استخدام Gemini للبحث الذكي
            model = genai.GenerativeModel('gemini-2.5-flash-lite-preview-06-17')

            search_prompt = f"""
            قم بالبحث عن معلومات حول: "{query}"

            أريد منك أن تقدم لي {num_results} نتائج بحث مفيدة ومتنوعة.
            لكل نتيجة، قدم:
            1. عنوان مناسب
            2. رابط (يمكن أن يكون تقديري إذا لم تكن متأكداً)
            3. ملخص مفيد للمحتوى

            قدم النتائج في تنسيق JSON كالتالي:
            {{
                "results": [
                    {{
                        "title": "عنوان النتيجة",
                        "url": "https://example.com",
                        "content": "ملخص المحتوى",
                        "engine": "gemini"
                    }}
                ],
                "query": "{query}",
                "number_of_results": {num_results}
            }}
            """

            response = await asyncio.to_thread(
                model.generate_content,
                search_prompt
            )

            # محاولة تحليل الاستجابة كـ JSON
            try:
                result_text = response.text.strip()
                # إزالة أي markdown formatting
                if result_text.startswith('```json'):
                    result_text = result_text[7:]
                if result_text.endswith('```'):
                    result_text = result_text[:-3]

                result_data = json.loads(result_text.strip())
                return result_data

            except json.JSONDecodeError:
                # إذا فشل التحليل، أنشئ نتيجة بسيطة
                return {
                    "results": [{
                        "title": f"نتائج البحث عن: {query}",
                        "url": "https://www.google.com/search?q=" + query.replace(" ", "+"),
                        "content": response.text[:200] + "...",
                        "engine": "gemini"
                    }],
                    "query": query,
                    "number_of_results": 1
                }

        except Exception as e:
            PrintStyle.error(f"خطأ في Gemini search: {str(e)}")
            # العودة لـ DuckDuckGo كبديل
            return await self._duckduckgo_search(query, num_results)

    async def _google_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """
        البحث باستخدام Google Custom Search API
        """
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            "key": self.google_api_key,
            "cx": self.google_search_engine_id,
            "q": query,
            "num": min(num_results, 10),  # Google API يدعم حد أقصى 10 نتائج لكل طلب
            "safe": "off"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._format_google_results(data)
                else:
                    error_text = await response.text()
                    raise Exception(f"Google API error {response.status}: {error_text}")

    async def _duckduckgo_search(self, query: str, num_results: int) -> Dict[str, Any]:
        """
        البحث باستخدام DuckDuckGo
        """
        # استخدم DuckDuckGo search helper الموجود
        results = await asyncio.to_thread(
            duckduckgo_search.search, 
            query, 
            results=num_results
        )
        
        return self._format_duckduckgo_results(results)

    def _format_google_results(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        تنسيق نتائج Google لتتوافق مع تنسيق SearXNG
        """
        results = []
        
        if "items" in data:
            for item in data["items"]:
                result = {
                    "title": item.get("title", ""),
                    "url": item.get("link", ""),
                    "content": item.get("snippet", ""),
                    "engine": "google"
                }
                results.append(result)
        
        return {
            "results": results,
            "query": data.get("queries", {}).get("request", [{}])[0].get("searchTerms", ""),
            "number_of_results": len(results)
        }

    def _format_duckduckgo_results(self, raw_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        تنسيق نتائج DuckDuckGo لتتوافق مع تنسيق SearXNG
        """
        results = []

        for raw_result in raw_results:
            try:
                if isinstance(raw_result, dict):
                    result = {
                        "title": raw_result.get("title", ""),
                        "url": raw_result.get("href", ""),
                        "content": raw_result.get("body", ""),
                        "engine": "duckduckgo"
                    }
                    results.append(result)
                else:
                    PrintStyle.error(f"نتيجة DuckDuckGo غير متوقعة: {type(raw_result)}")
            except Exception as e:
                PrintStyle.error(f"خطأ في تنسيق نتيجة DuckDuckGo: {str(e)}")
                continue

        return {
            "results": results,
            "query": "",
            "number_of_results": len(results)
        }


# إنشاء instance واحد للاستخدام
_local_search_instance = None

async def search(query: str, num_results: int = 10) -> Dict[str, Any]:
    """
    دالة البحث الرئيسية - بديل مباشر لـ searxng.search
    """
    global _local_search_instance
    
    if _local_search_instance is None:
        _local_search_instance = LocalSearchEngine()
    
    return await _local_search_instance.search(query, num_results)


# للتوافق مع الكود الحالي
async def local_search(query: str) -> Dict[str, Any]:
    """
    دالة مساعدة للتوافق مع الكود الحالي
    """
    return await search(query)
