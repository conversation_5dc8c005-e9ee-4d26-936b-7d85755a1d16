#!/usr/bin/env python3
"""
اختبار البحث باستخدام Gemini
"""
import asyncio
import sys
import os

# إضافة مسار المشروع للـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python.helpers.local_search import search

async def test_gemini_search():
    """
    اختبار البحث باستخدام Gemini
    """
    print("🔍 اختبار البحث باستخدام Gemini...")
    
    try:
        query = "artificial intelligence latest developments"
        print(f"البحث عن: {query}")
        
        results = await search(query, num_results=3)
        
        print(f"✅ تم العثور على {len(results.get('results', []))} نتيجة")
        
        for i, result in enumerate(results.get('results', [])[:3]):
            print(f"\n--- النتيجة {i+1} ---")
            print(f"العنوان: {result.get('title', 'غير متوفر')}")
            print(f"الرابط: {result.get('url', 'غير متوفر')}")
            print(f"المحتوى: {result.get('content', 'غير متوفر')[:150]}...")
            print(f"المحرك: {result.get('engine', 'غير محدد')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_gemini_search())
