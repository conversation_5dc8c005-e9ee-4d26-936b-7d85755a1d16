# Project Templates & Quick Start Commands

## Mobile App Projects
### Flutter (Recommended for cross-platform)
```bash
# Quick Setup (30 seconds)
flutter create app_name
cd app_name
flutter pub get

# Basic Structure Creation
mkdir lib/screens lib/widgets lib/services lib/models
# Create main screens immediately
# Add navigation
# Implement core features
```

### React Native (Alternative)
```bash
npx react-native init AppName
cd AppName
npm install
# Setup navigation and basic screens
```

## Web Application Projects
### Full-Stack Web App (Laravel + Vue.js)
```bash
# Backend Setup
composer create-project laravel/laravel backend
cd backend
php artisan serve

# Frontend Setup (parallel terminal)
npm create vue@latest frontend
cd frontend
npm install && npm run dev
```

### Simple Web App (HTML/CSS/JS)
```bash
mkdir webapp && cd webapp
touch index.html style.css script.js
# Create basic structure immediately
# Add functionality incrementally
```

### React Web App
```bash
npx create-react-app app-name
cd app-name
npm start
# Setup routing and components
```

## Desktop Applications
### Electron (Web Technologies)
```bash
mkdir desktop-app && cd desktop-app
npm init -y
npm install electron --save-dev
# Create main.js and index.html
# Package for multiple platforms
```

### Python Desktop (tkinter)
```bash
mkdir desktop-app && cd desktop-app
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install tkinter
# Create main.py with GUI
```

## API Projects
### FastAPI (Python - Fastest)
```bash
mkdir api-project && cd api-project
python -m venv venv
source venv/bin/activate
pip install fastapi uvicorn
# Create main.py with endpoints
uvicorn main:app --reload
```

### Express.js (Node.js)
```bash
mkdir api-project && cd api-project
npm init -y
npm install express cors dotenv
# Create server.js
# Add routes and middleware
```

### Laravel API (PHP)
```bash
composer create-project laravel/laravel api-project
cd api-project
php artisan serve
# Create API routes and controllers
```

## Automation Scripts
### Python Automation
```bash
mkdir automation && cd automation
python -m venv venv
source venv/bin/activate
pip install requests beautifulsoup4 selenium pandas
# Create main script
```

### Node.js Automation
```bash
mkdir automation && cd automation
npm init -y
npm install puppeteer axios cheerio
# Create automation scripts
```

## Game Development
### Unity (C#)
```bash
# Create new Unity project
# Setup basic scene
# Add player controller
# Implement core mechanics
```

### Godot (GDScript)
```bash
# Create new Godot project
# Setup main scene
# Add player and basic mechanics
```

## Data Science Projects
### Python Data Analysis
```bash
mkdir data-project && cd data-project
python -m venv venv
source venv/bin/activate
pip install pandas numpy matplotlib seaborn jupyter
jupyter notebook
```

## E-commerce Projects
### Laravel E-commerce
```bash
composer create-project laravel/laravel ecommerce
cd ecommerce
# Install packages: spatie/laravel-permission, intervention/image
# Create models: Product, Category, Order, User
# Setup admin panel and frontend
```

## Blog/CMS Projects
### WordPress (Fastest for blogs)
```bash
# Download WordPress
# Setup database
# Configure wp-config.php
# Install and customize theme
```

### Laravel Blog
```bash
composer create-project laravel/laravel blog
cd blog
# Create models: Post, Category, Tag, Comment
# Setup admin panel
# Create frontend views
```

## Real-time Applications
### Socket.io Chat App
```bash
mkdir chat-app && cd chat-app
npm init -y
npm install express socket.io
# Create server with socket handling
# Create client-side chat interface
```

## Machine Learning Projects
### Python ML Project
```bash
mkdir ml-project && cd ml-project
python -m venv venv
source venv/bin/activate
pip install scikit-learn pandas numpy matplotlib tensorflow
# Create data processing pipeline
# Train and evaluate model
```

## Blockchain Projects
### Smart Contract (Solidity)
```bash
mkdir blockchain-project && cd blockchain-project
npm init -y
npm install hardhat @openzeppelin/contracts
npx hardhat init
# Create smart contracts
# Write tests and deployment scripts
```

## Quick Decision Matrix

| Project Type | Best Tool | Setup Time | Complexity |
|-------------|-----------|------------|------------|
| Mobile App | Flutter | 2 min | Medium |
| Web App | React/Laravel | 3 min | Medium |
| Desktop | Electron | 2 min | Low |
| API | FastAPI | 1 min | Low |
| Automation | Python | 1 min | Low |
| Game | Unity | 5 min | High |
| Data Science | Jupyter | 2 min | Medium |
| E-commerce | Laravel | 5 min | High |
| Blog | WordPress | 3 min | Low |
| Real-time | Socket.io | 2 min | Medium |
| ML | Python | 2 min | High |
| Blockchain | Hardhat | 3 min | High |

## Success Metrics
- **Setup completed in under 5 minutes**
- **Working prototype in under 30 minutes**
- **Core features implemented in under 2 hours**
- **Deployable version in under 1 day**
