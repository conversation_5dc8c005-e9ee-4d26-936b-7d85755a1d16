"""
بديل محلي لـ SearXNG - يستخدم Google Custom Search API أو DuckDuckGo
"""
from python.helpers import runtime
from python.helpers.local_search import search as local_search_func

async def search(query: str):
    """
    دالة البحث الرئيسية - بديل مباشر لـ SearXNG
    تستخدم الخدمة المحلية الجديدة بدلاً من SearXNG على البورت 55510
    """
    return await runtime.call_development_function(_search, query=query)

async def _search(query: str):
    """
    تنفيذ البحث باستخدام الخدمة المحلية
    """
    return await local_search_func(query)
