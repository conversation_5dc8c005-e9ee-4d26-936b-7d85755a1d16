"""
اختبار تكامل خدمة البحث مع Agent Zero
"""
import asyncio
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_integration():
    print("🔍 اختبار تكامل خدمة البحث...")
    
    try:
        # اختبار import للخدمة الجديدة
        from python.helpers.local_search import search
        print("✅ تم استيراد local_search بنجاح")
        
        # اختبار import لـ searxng المحدث
        from python.helpers.searxng import search as searxng_search
        print("✅ تم استيراد searxng المحدث بنجاح")
        
        # اختبار import لأدوات البحث
        from python.tools.search_engine import SearchEngine
        print("✅ تم استيراد SearchEngine بنجاح")
        
        print("\n🎉 جميع الـ imports نجحت! الخدمة جاهزة للاستخدام")
        print("📝 يمكنك الآن استخدام Agent Zero مع خدمة البحث المحلية")
        print("🌐 افتح http://localhost:50001 وجرب البحث")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التكامل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_integration())
